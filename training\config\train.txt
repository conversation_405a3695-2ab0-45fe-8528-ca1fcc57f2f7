device = cuda
basedir = /home/<USER>/vitfly_ws/src/vitfly
logdir = training/logs
datadir = training/datasets

dataset = data
short = 0
val_split = 0.2

model_type = ViTLSTM
load_checkpoint = False
checkpoint_path = ''

lr = 1e-4
N_eps = 100
lr_warmup_epochs = 5
lr_decay = False
save_model_freq = 25
val_freq = 10

# --- Reinforcement Learning (TD3) Configuration ---
# Enable Reinforcement Learning stage
enable_rl = False
# Path to the pre-trained Imitation Learning model (for RL initialization)
il_checkpoint_path_for_rl = ''

# TD3 Hyperparameters
rl_actor_lr = 1e-4
rl_critic_lr = 1e-3
rl_gamma = 0.99  # Discount factor
rl_tau = 0.005   # Soft update coefficient
rl_policy_noise = 0.2 # Noise for target policy smoothing
rl_noise_clip = 0.5   # Noise clipping for target policy smoothing
rl_policy_freq = 2    # Delayed policy update frequency
rl_buffer_size = 1000000
rl_batch_size = 256
rl_exploration_noise = 0.1 # Stddev for Gaussian exploration noise

# RL Training
rl_N_eps = 200 # Number of epochs/episodes for RL training
rl_max_episode_steps = 1000 # Max steps per RL episode

# Reward Weights (to be tuned)
reward_weight_survival = 1.0
reward_weight_goal_velocity = 0.5
reward_weight_forward_motion = 0.5
reward_weight_safety_distance = -1.0 # Negative because it's often a penalty
reward_weight_action_smoothness = -0.01
reward_penalty_collision = -100.0
reward_penalty_boundary = -50.0
